import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserOrder, UserConvertCustomer } from '@modules/business/entities'; // UserConvertCustomer might be implicitly used by admin's join logic
import { PaginatedResult } from '@common/response'; // Using user's simpler path
import { QueryUserOrderDto as QueryUserOrderDtoUser } from '@modules/business/user/dto'; // Aliased DTO
import { QueryUserOrderDto as QueryUserOrderDtoAdmin } from '@modules/business/admin/dto'; // Aliased DTO

/**
 * Repository xử lý truy vấn dữ liệu cho entity UserOrder,
 * kết hợp chức năng từ cả user và admin context.
 */
@Injectable()
export class UserOrderRepository extends Repository<UserOrder> {
  private readonly logger = new Logger(UserOrderRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserOrder, dataSource.createEntityManager());
  }

  // --- Base Query Builders ---

  /**
   * Tạo truy vấn cơ bản cho bảng user_orders (User context version)
   * @returns QueryBuilder cho bảng user_orders
   */
  private createBaseQuery_user(): SelectQueryBuilder<UserOrder> {
    return this.createQueryBuilder('user_order'); // Changed to non-reserved alias
  }

  /**
   * Tạo query builder cơ bản cho bảng user_orders (Admin context version)
   */
  private createBaseQuery_admin(): SelectQueryBuilder<UserOrder> {
    return this.createQueryBuilder('user_order'); // Admin's alias
  }

  // --- Methods from User File ---

  /**
   * Tạo đơn hàng mới
   * @param orderData Dữ liệu đơn hàng
   * @returns Đơn hàng đã tạo
   */
  async createOrder(orderData: Partial<UserOrder>): Promise<UserOrder> {
    try {
      this.logger.log('Tạo đơn hàng mới');

      const order = this.create(orderData);
      const savedOrder = await this.save(order);

      this.logger.log(`Đã tạo đơn hàng thành công với ID=${savedOrder.id}`);
      return savedOrder;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo đơn hàng: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo đơn hàng: ${error.message}`);
    }
  }

  /**
   * Tìm đơn hàng theo ID (User context method)
   * @param id ID đơn hàng
   * @returns Đơn hàng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<UserOrder | null> {
    try {
      return await this.createBaseQuery_user() // Use user base query
        .where('user_order.id = :id', { id })       // Updated alias
        .getOne();
    } catch (error) {
      this.logger.error(`(User) Lỗi khi tìm đơn hàng theo ID ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm đơn hàng theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm danh sách đơn hàng với phân trang (User context method)
   * @param userId ID người dùng (specific to user context filtering)
   * @param queryDto Tham số truy vấn (User DTO)
   * @returns Danh sách đơn hàng với phân trang
   */
  async findAll(userId: number, queryDto: QueryUserOrderDtoUser): Promise<PaginatedResult<any>> {
    try {
      const {
        page = 1,
        limit = 10,
        userConvertCustomerId,
        shippingStatus,
        source,
        orderStatus,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
      } = queryDto;

      const skip = (page - 1) * limit;

      const query = this.createBaseQuery_user()
        .leftJoinAndSelect('user_order.userConvertCustomer', 'customer')
        .where('user_order.userId = :userId', { userId });

      if (userConvertCustomerId) {
        query.andWhere('user_order.userConvertCustomerId = :userConvertCustomerId', { userConvertCustomerId });
      }
      if (shippingStatus) {
        query.andWhere('user_order.shippingStatus = :shippingStatus', { shippingStatus });
      }
      if (source) {
        query.andWhere('user_order.source = :source', { source });
      }
      if (orderStatus) {
        query.andWhere('user_order.orderStatus = :orderStatus', { orderStatus });
      }

      query.orderBy(`user_order.${sortBy}`, sortDirection);
      query.skip(skip).take(limit);

      const [items, total] = await query.getManyAndCount();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`(User) Lỗi khi lấy danh sách đơn hàng: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy danh sách đơn hàng: ${error.message}`);
    }
  }

  /**
   * Tìm đơn hàng theo tracking number
   * @param trackingNumber Mã vận đơn
   * @returns Đơn hàng hoặc null nếu không tìm thấy
   */
  async findByTrackingNumber(trackingNumber: string): Promise<UserOrder | null> {
    try {
      this.logger.log(`Tìm đơn hàng theo tracking number: ${trackingNumber}`);

      return await this.createBaseQuery_user()
        .where("user_order.logisticInfo->>'trackingNumber' = :trackingNumber", { trackingNumber })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm đơn hàng theo tracking number ${trackingNumber}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm đơn hàng theo tracking number ${trackingNumber}: ${error.message}`);
    }
  }

  /**
   * Cập nhật đơn hàng
   * @param id ID đơn hàng
   * @param updateData Dữ liệu cập nhật
   * @returns Đơn hàng đã cập nhật
   */
  async updateOrder(id: number, updateData: Partial<UserOrder>): Promise<UserOrder> {
    try {
      this.logger.log(`Cập nhật đơn hàng ID: ${id}`);

      // Cập nhật updatedAt
      const dataToUpdate = {
        ...updateData,
        updatedAt: Date.now()
      };

      await this.userOrderRepository.update(id, dataToUpdate);

      const updatedOrder = await this.findById(id);
      if (!updatedOrder) {
        throw new Error(`Không tìm thấy đơn hàng sau khi cập nhật: ${id}`);
      }

      this.logger.log(`Đã cập nhật đơn hàng ID: ${id} thành công`);
      return updatedOrder;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật đơn hàng ${id}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật đơn hàng ${id}: ${error.message}`);
    }
  }

  // --- Methods from Admin File ---

  /**
   * Tìm đơn hàng theo ID (Admin context method)
   */
  async findUserOrderById(id: number): Promise<UserOrder | null> {
    this.logger.log(`(Admin) Tìm đơn hàng với ID: ${id}`);
    const result = await this.createBaseQuery_admin() // Use admin base query
      .where('user_order.id = :id', { id })       // Admin alias
      .getOne();
    this.logger.log(
      result ? `(Admin) Đã tìm thấy đơn hàng với ID: ${id}` : `(Admin) Không tìm thấy đơn hàng với ID: ${id}`
    );
    return result;
  }

  /**
   * Tìm đơn hàng theo ID với thông tin khách hàng (Admin context method)
   * Lấy tất cả thông tin của các trường trong bảng user_orders và user_convert_customers
   */
  async findUserOrderByIdWithCustomer(id: number): Promise<Record<string, any> | null> {
    this.logger.log(`(Admin) findUserOrderByIdWithCustomer: Tìm đơn hàng với ID: ${id} kèm thông tin khách hàng`);
    try {
      this.logger.log(`(Admin) findUserOrderByIdWithCustomer: Tạo query builder với join customer`);
      const queryBuilder = this.createBaseQuery_admin() // Use admin base query
        .where('user_order.id = :id', { id });                 // Admin alias

      const userOrder = await queryBuilder.getOne();

      if (!userOrder) {
        this.logger.log(`(Admin) findUserOrderByIdWithCustomer: Không tìm thấy đơn hàng với ID: ${id}`);
        return null;
      }
      this.logger.log(`(Admin) findUserOrderByIdWithCustomer: Đã tìm thấy đơn hàng với ID: ${id} kèm thông tin khách hàng`);

      // Manual mapping as in original admin file
      const result: Record<string, any> = {
        id: userOrder.id,
        user_convert_customer_id: userOrder.userConvertCustomerId,
        user_id: userOrder.userId,
        product_info: userOrder.productInfo,
        bill_info: userOrder.billInfo,
        has_shipping: userOrder.hasShipping,
        shipping_status: userOrder.shippingStatus,
        logistic_info: userOrder.logisticInfo,
        created_at: userOrder.createdAt,
        updated_at: userOrder.updatedAt,
        source: userOrder.source
      };

      // Get customer data from the relationship if available
      const customer = await this.dataSource
        .getRepository(UserConvertCustomer)
        .findOne({ where: { id: userOrder.userConvertCustomerId } });

      if (customer) {
        result.customer_id = customer.id;
        result.customer_name = customer.name;
        result.customer_email = customer.email;
        result.customer_phone = customer.phone;
        result.customer_avatar = customer.avatar;
        result.customer_platform = customer.platform;
        result.customer_timezone = customer.timezone;
        result.customer_metadata = customer.metadata;
        result.customer_created_at = customer.createdAt;
        result.customer_updated_at = customer.updatedAt;
        result.customer_user_id = customer.userId;
        result.customer_agent_id = customer.agentId;
      }
      return result;
    } catch (error) {
      this.logger.error(`(Admin) findUserOrderByIdWithCustomer: Lỗi khi tìm đơn hàng với ID ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tìm danh sách đơn hàng với phân trang, tìm kiếm và lọc (Admin context method)
   * Lấy tất cả thông tin của các trường trong bảng user_orders
   */
  async findUserOrders(queryParams: QueryUserOrderDtoAdmin): Promise<PaginatedResult<UserOrder>> {
    this.logger.log(`(Admin) findUserOrders: Tìm kiếm đơn hàng với các tham số: ${JSON.stringify(queryParams)}`);
    try { // Added try-catch for consistency, though original admin didn't have it at this top level
      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        userId,
        userConvertCustomerId,
        hasShipping,
        shippingStatus,
        source,
        createdAtFrom,
        createdAtTo
      } = queryParams;

      const skip = (page - 1) * limit;

      this.logger.log(`(Admin) findUserOrders: Tạo query builder với join bảng user_convert_customers`);
      const queryBuilder = this.createBaseQuery_admin() // Use admin base query
        .leftJoin('user_convert_customers', 'customer', 'customer.id = user_order.userConvertCustomerId') // Join trực tiếp với bảng user_convert_customers
        .select([ // Manual select as in original admin file
          'user_order.id as id',
          'user_order.userConvertCustomerId as user_convert_customer_id', // Using entity property names
          'user_order.userId as user_id',
          'user_order.productInfo as product_info',
          'user_order.billInfo as bill_info',
          'user_order.hasShipping as has_shipping',
          'user_order.shippingStatus as shipping_status',
          'user_order.logisticInfo as logistic_info',
          'user_order.createdAt as created_at',
          'user_order.updatedAt as updated_at',
          'user_order.source as source',
          'customer.id as customer_id',
          'customer.name as customer_name',
          'customer.phone as customer_phone'
        ]);

      this.logger.log(`(Admin) findUserOrders: Áp dụng các điều kiện tìm kiếm`);
      if (search) {
        queryBuilder.andWhere('(customer.name ILIKE :search OR customer.phone ILIKE :search)', { search: `%${search}%` });
      }
      if (userId) {
        queryBuilder.andWhere('user_order.userId = :userId', { userId }); // Admin alias
      }
      if (userConvertCustomerId) {
        queryBuilder.andWhere('user_order.userConvertCustomerId = :userConvertCustomerId', { userConvertCustomerId }); // Admin alias
      }
      if (hasShipping !== undefined) {
        queryBuilder.andWhere('user_order.hasShipping = :hasShipping', { hasShipping }); // Admin alias
      }
      if (shippingStatus) {
        queryBuilder.andWhere('user_order.shippingStatus ILIKE :shippingStatus', { shippingStatus }); // Admin alias
      }
      if (source) {
        queryBuilder.andWhere('user_order.source ILIKE :source', { source }); // Admin alias
      }
      if (createdAtFrom) {
        queryBuilder.andWhere('user_order.createdAt >= :createdAtFrom', { createdAtFrom: Number(createdAtFrom) }); // Sử dụng số thay vì Date object
      }
      if (createdAtTo) {
        queryBuilder.andWhere('user_order.createdAt <= :createdAtTo', { createdAtTo: Number(createdAtTo) }); // Sử dụng số thay vì Date object
      }

      this.logger.log(`(Admin) findUserOrders: Đếm tổng số bản ghi thỏa mãn điều kiện`);
      // For getCount() to work correctly with joins and filters, it might need similar where clauses
      // or a subquery. The original admin code might have issues here if filters are complex.
      // A safer way for count with complex raw queries and joins:
      const countQueryBuilder = this.createBaseQuery_admin().leftJoin('user_convert_customers', 'customer', 'customer.id = user_order.userConvertCustomerId');
      if (search) { countQueryBuilder.andWhere('(customer.name ILIKE :search OR customer.phone ILIKE :search)', { search: `%${search}%` }); }
      if (userId) { countQueryBuilder.andWhere('user_order.userId = :userId', { userId }); }
      if (userConvertCustomerId) { countQueryBuilder.andWhere('user_order.userConvertCustomerId = :userConvertCustomerId', { userConvertCustomerId }); }
      if (hasShipping !== undefined) { countQueryBuilder.andWhere('user_order.hasShipping = :hasShipping', { hasShipping }); }
      if (shippingStatus) { countQueryBuilder.andWhere('user_order.shippingStatus ILIKE :shippingStatus', { shippingStatus }); }
      if (source) { countQueryBuilder.andWhere('user_order.source ILIKE :source', { source }); }
      if (createdAtFrom) { countQueryBuilder.andWhere('user_order.createdAt >= :createdAtFrom', { createdAtFrom: Number(createdAtFrom) }); }
      if (createdAtTo) { countQueryBuilder.andWhere('user_order.createdAt <= :createdAtTo', { createdAtTo: Number(createdAtTo) }); }
      const totalItems = await countQueryBuilder.getCount();


      const columnMapping = { // Preserving admin's original column mapping logic
        id: 'id',
        userConvertCustomerId: 'userConvertCustomerId', // Using entity property names for sortBy
        userId: 'userId',
        productInfo: 'productInfo',
        billInfo: 'billInfo',
        hasShipping: 'hasShipping',
        shippingStatus: 'shippingStatus',
        logisticInfo: 'logisticInfo',
        createdAt: 'createdAt',
        updatedAt: 'updatedAt',
        source: 'source'
      };
      const sortProperty = columnMapping[sortBy] || 'createdAt';
      this.logger.log(`(Admin) findUserOrders: Sắp xếp theo cột ${sortProperty} theo hướng ${sortDirection}`);

      const itemsRaw = await queryBuilder
        .orderBy(`user_order.${sortProperty}`, sortDirection) // Admin alias
        .offset(skip)
        .limit(limit)
        .getRawMany();
      this.logger.log(`(Admin) findUserOrders: Đã tìm thấy ${itemsRaw.length} bản ghi`);

      const orders = itemsRaw.map(item => { // Manual mapping from raw as in original admin file
        const order = new UserOrder();
        order.id = Number(item.id);
        // Use the alias from the .select() statement for raw properties
        order.userConvertCustomerId = item.user_convert_customer_id ? Number(item.user_convert_customer_id) : 0;
        order.userId = item.user_id ? Number(item.user_id) : 0;
        order.productInfo = item.product_info;
        order.billInfo = item.bill_info;
        order.hasShipping = item.has_shipping === 'true' || item.has_shipping === true || item.has_shipping === 1; // Boolean conversion
        order.shippingStatus = item.shipping_status;
        order.logisticInfo = item.logistic_info;
        order.createdAt = Number(item.created_at) || Date.now();
        order.updatedAt = Number(item.updated_at) || Date.now();
        order.source = item.source;
        // Customer info is not directly part of UserOrder entity here, it was for search/filter
        return order;
      });

      return {
        items: orders,
        meta: {
          totalItems,
          itemCount: itemsRaw.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(totalItems / limit),
          currentPage: page
        }
      };
    } catch (error) {
      this.logger.error(`(Admin) findUserOrders: Lỗi khi tìm kiếm đơn hàng: ${error.message}`, error.stack);
      throw error; // Re-throw error as per admin's original style
    }
  }

  /**
   * Lấy thống kê trạng thái đơn hàng và vận chuyển của người dùng
   * @param userId ID người dùng
   * @returns Thống kê trạng thái
   */
  async getOrderStatusStats(userId: number): Promise<{
    orderStatus: Record<string, number>;
    shippingStatus: Record<string, number>;
    totalOrders: number;
    ordersWithShipping: number;
    ordersWithoutShipping: number;
  }> {
    try {
      this.logger.log(`Lấy thống kê trạng thái đơn hàng cho userId=${userId}`);

      // Thống kê trạng thái đơn hàng (tất cả đơn hàng, không phân biệt hasShipping)
      const orderStatusQuery = this.createBaseQuery_user()
        .select('user_order.orderStatus', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('user_order.userId = :userId', { userId })
        .groupBy('user_order.orderStatus');

      const orderStatusResults = await orderStatusQuery.getRawMany();

      // Thống kê trạng thái vận chuyển (chỉ đơn hàng có vận chuyển)
      const shippingStatusQuery = this.createBaseQuery_user()
        .select('user_order.shippingStatus', 'status')
        .addSelect('COUNT(*)', 'count')
        .where('user_order.userId = :userId', { userId })
        .andWhere('user_order.hasShipping = :hasShipping', { hasShipping: true })
        .groupBy('user_order.shippingStatus');

      const shippingStatusResults = await shippingStatusQuery.getRawMany();

      // Đếm tổng số đơn hàng
      const totalOrdersResult = await this.createBaseQuery_user()
        .select('COUNT(*)', 'total')
        .where('user_order.userId = :userId', { userId })
        .getRawOne();

      // Đếm số đơn hàng có vận chuyển
      const ordersWithShippingResult = await this.createBaseQuery_user()
        .select('COUNT(*)', 'total')
        .where('user_order.userId = :userId', { userId })
        .andWhere('user_order.hasShipping = :hasShipping', { hasShipping: true })
        .getRawOne();

      // Đếm số đơn hàng không có vận chuyển
      const ordersWithoutShippingResult = await this.createBaseQuery_user()
        .select('COUNT(*)', 'total')
        .where('user_order.userId = :userId', { userId })
        .andWhere('user_order.hasShipping = :hasShipping', { hasShipping: false })
        .getRawOne();

      // Chuyển đổi kết quả thành object
      const orderStatus = orderStatusResults.reduce((acc, item) => {
        if (item.status) {
          acc[item.status] = parseInt(item.count);
        }
        return acc;
      }, {});

      const shippingStatus = shippingStatusResults.reduce((acc, item) => {
        if (item.status) {
          acc[item.status] = parseInt(item.count);
        }
        return acc;
      }, {});

      const totalOrders = parseInt(totalOrdersResult?.total || '0');
      const ordersWithShipping = parseInt(ordersWithShippingResult?.total || '0');
      const ordersWithoutShipping = parseInt(ordersWithoutShippingResult?.total || '0');

      this.logger.log(`Thống kê đơn hàng userId=${userId}: total=${totalOrders}, withShipping=${ordersWithShipping}, withoutShipping=${ordersWithoutShipping}`);

      return {
        orderStatus,
        shippingStatus,
        totalOrders,
        ordersWithShipping,
        ordersWithoutShipping,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy thống kê trạng thái đơn hàng: ${error.message}`);
    }
  }
}